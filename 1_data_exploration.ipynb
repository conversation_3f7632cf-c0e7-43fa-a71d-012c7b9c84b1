# Import des librairies de base
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime

# Configuration des visualisations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Import des utilitaires du projet
from utils.notebook_init import setup_notebook
from utils.data_tools import load_data, basic_info
from utils.preprocessing import detect_outliers, handle_missing_values
from utils.visualization_optimizer import create_distribution_plots

# Configuration du notebook
setup_notebook()

# Chargement des données depuis la base SQLite
data_path = 'data/raw/olist.db'

# TODO: Charger les données client depuis la base
# df = load_data(data_path)

print(f"Données chargées depuis : {data_path}")

# TODO: Aperçu rapide des données
# print(f"Shape du dataset : {df.shape}")
# print(f"\nTypes de données :")
# print(df.dtypes)
# print(f"\nPremières lignes :")
# display(df.head())

# Utilisation de l'utilitaire
# basic_info(df)

# TODO: Vérification des doublons
# print(f"Nombre de doublons : {df.duplicated().sum()}")
# print(f"Nombre de customer_id uniques : {df['customer_id'].nunique()}")
# print(f"Nombre total de lignes : {len(df)}")

# Vérification de la clé primaire
# if df['customer_id'].nunique() == len(df):
#     print("✓ customer_id est bien une clé primaire")
# else:
#     print("⚠️ customer_id n'est pas unique")

# TODO: Analyse des valeurs manquantes
# missing_data = df.isnull().sum()
# missing_percent = (missing_data / len(df)) * 100
# missing_df = pd.DataFrame({
#     'Colonne': missing_data.index,
#     'Valeurs_manquantes': missing_data.values,
#     'Pourcentage': missing_percent.values
# })
# missing_df = missing_df[missing_df['Valeurs_manquantes'] > 0].sort_values('Pourcentage', ascending=False)
# print(missing_df)

# TODO: Heatmap des valeurs manquantes
# plt.figure(figsize=(12, 8))
# sns.heatmap(df.isnull(), cbar=True, cmap='viridis', yticklabels=False)
# plt.title('Heatmap des valeurs manquantes')
# plt.xticks(rotation=45)
# plt.tight_layout()
# plt.show()

# TODO: Traitement des valeurs manquantes
# df_clean = handle_missing_values(df, strategy='drop', threshold=0.5)
# print(f"Shape après nettoyage : {df_clean.shape}")

# TODO: Histogrammes + KDE pour variables numériques
# numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
# create_distribution_plots(df_clean, numeric_columns)

# Exemple manuel pour une variable
# plt.figure(figsize=(15, 5))
# for i, col in enumerate(numeric_columns[:3], 1):
#     plt.subplot(1, 3, i)
#     df_clean[col].hist(bins=50, alpha=0.7, edgecolor='black')
#     plt.title(f'Distribution de {col}')
#     plt.xlabel(col)
#     plt.ylabel('Fréquence')
# plt.tight_layout()
# plt.show()

# TODO: Countplots pour variables qualitatives
# categorical_columns = df_clean.select_dtypes(include=['object']).columns
#
# plt.figure(figsize=(15, 10))
# for i, col in enumerate(categorical_columns, 1):
#     plt.subplot(2, 3, i)
#     df_clean[col].value_counts().plot(kind='bar')
#     plt.title(f'Distribution de {col}')
#     plt.xticks(rotation=45)
# plt.tight_layout()
# plt.show()

# TODO: Statistiques descriptives avancées
# desc_stats = df_clean.describe()
# print("Statistiques descriptives :")
# display(desc_stats)

# Calcul des mesures de forme
# from scipy import stats
# skewness = df_clean.select_dtypes(include=[np.number]).skew()
# kurtosis = df_clean.select_dtypes(include=[np.number]).kurtosis()
#
# shape_stats = pd.DataFrame({
#     'Skewness': skewness,
#     'Kurtosis': kurtosis
# })
# print("\nMesures de forme (asymétrie et aplatissement) :")
# display(shape_stats)

# TODO: Matrice de corrélation
# numeric_df = df_clean.select_dtypes(include=[np.number])
# correlation_matrix = numeric_df.corr()
#
# plt.figure(figsize=(12, 10))
# sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
#             square=True, linewidths=0.5)
# plt.title('Matrice de corrélation des variables numériques')
# plt.tight_layout()
# plt.show()

# TODO: Scatterplots pour relations importantes
# # Exemple : relation entre montant et fréquence
# if 'total_amount' in df_clean.columns and 'frequency' in df_clean.columns:
#     plt.figure(figsize=(10, 6))
#     plt.scatter(df_clean['frequency'], df_clean['total_amount'], alpha=0.6)
#     plt.xlabel('Fréquence d\'achat')
#     plt.ylabel('Montant total')
#     plt.title('Relation entre fréquence et montant')
#     plt.show()

# TODO: Boxplots entre variables quantitatives et qualitatives
# # Exemple : montant par catégorie
# if 'category' in df_clean.columns and 'total_amount' in df_clean.columns:
#     plt.figure(figsize=(12, 6))
#     sns.boxplot(data=df_clean, x='category', y='total_amount')
#     plt.title('Distribution du montant par catégorie')
#     plt.xticks(rotation=45)
#     plt.tight_layout()
#     plt.show()

# TODO: Pairplot ciblé sur variables candidates pour segmentation
# # Sélection des variables les plus importantes pour la segmentation
# key_variables = ['recency', 'frequency', 'monetary']  # Variables RFM
#
# if all(var in df_clean.columns for var in key_variables):
#     sns.pairplot(df_clean[key_variables], diag_kind='kde')
#     plt.suptitle('Relations entre variables RFM', y=1.02)
#     plt.tight_layout()
#     plt.show()

# TODO: Préparation ACP si nécessaire
# from sklearn.decomposition import PCA
# from sklearn.preprocessing import StandardScaler
#
# # Standardisation des données pour ACP
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(numeric_df)
#
# # ACP exploratoire
# pca = PCA()
# pca.fit(X_scaled)
#
# # Variance expliquée
# plt.figure(figsize=(10, 6))
# plt.bar(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_)
# plt.xlabel('Composantes principales')
# plt.ylabel('Variance expliquée')
# plt.title('Variance expliquée par composante')
# plt.show()

# TODO: Détection des outliers
# outliers_info = detect_outliers(df_clean, methods=['zscore', 'iqr'])
# print("Outliers détectés :")
# for col, outliers in outliers_info.items():
#     print(f"{col}: {len(outliers)} outliers ({len(outliers)/len(df_clean)*100:.2f}%)")

# TODO: Visualisation des outliers avec boxplots
# numeric_cols = df_clean.select_dtypes(include=[np.number]).columns
#
# plt.figure(figsize=(15, 10))
# for i, col in enumerate(numeric_cols, 1):
#     plt.subplot(2, 3, i)
#     sns.boxplot(y=df_clean[col])
#     plt.title(f'Boxplot de {col}')
# plt.tight_layout()
# plt.show()

# TODO: Traitement des outliers selon pertinence métier
# # Définir les seuils selon le contexte business
# df_no_outliers = df_clean.copy()
#
# # Exemple : suppression des outliers extrêmes (Z-score > 3)
# from scipy import stats
# for col in numeric_cols:
#     z_scores = np.abs(stats.zscore(df_clean[col].dropna()))
#     df_no_outliers = df_no_outliers[z_scores < 3]
#
# print(f"Dataset original : {df_clean.shape}")
# print(f"Dataset sans outliers extrêmes : {df_no_outliers.shape}")
# print(f"Suppression de {df_clean.shape[0] - df_no_outliers.shape[0]} lignes")

# TODO: Calcul de l'ancienneté client
# from utils.feature_engineering import calculate_customer_age
#
# # Si on a une colonne de date d'inscription
# if 'first_purchase_date' in df_no_outliers.columns:
#     df_no_outliers['customer_age_days'] = calculate_customer_age(
#         df_no_outliers['first_purchase_date']
#     )
#     print(f"Ancienneté moyenne : {df_no_outliers['customer_age_days'].mean():.0f} jours")

# TODO: Calcul des métriques d'achat
# # Taux d'achat, panier moyen, etc.
# if 'total_amount' in df_no_outliers.columns and 'frequency' in df_no_outliers.columns:
#     df_no_outliers['average_basket'] = df_no_outliers['total_amount'] / df_no_outliers['frequency']
#
#     print("Métriques calculées :")
#     print(f"- Panier moyen : {df_no_outliers['average_basket'].mean():.2f}")
#     print(f"- Fréquence moyenne : {df_no_outliers['frequency'].mean():.2f}")

# TODO: Conversion des colonnes de dates
# date_columns = df_no_outliers.select_dtypes(include=['datetime64']).columns
# print(f"Colonnes de dates trouvées : {list(date_columns)}")
#
# # Extraction de features temporelles si nécessaire
# for col in date_columns:
#     df_no_outliers[f'{col}_year'] = df_no_outliers[col].dt.year
#     df_no_outliers[f'{col}_month'] = df_no_outliers[col].dt.month
#     df_no_outliers[f'{col}_weekday'] = df_no_outliers[col].dt.weekday

# TODO: Sauvegarde du dataset nettoyé
# from utils.save_load import save_dataframe
#
# # Export CSV
# output_path = 'data/processed/cleaned_data.csv'
# df_no_outliers.to_csv(output_path, index=False)
# print(f"Dataset nettoyé sauvegardé : {output_path}")
#
# # Export pickle pour préserver les types
# pickle_path = 'data/processed/cleaned_data.pkl'
# df_no_outliers.to_pickle(pickle_path)
# print(f"Dataset pickle sauvegardé : {pickle_path}")

# TODO: Création du log de version
# metadata = {
#     'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
#     'original_shape': df.shape,
#     'cleaned_shape': df_no_outliers.shape,
#     'columns': list(df_no_outliers.columns),
#     'missing_values_handled': True,
#     'outliers_removed': True,
#     'feature_engineering': 'basic'
# }
#
# import json
# with open('data/processed/cleaning_log.json', 'w') as f:
#     json.dump(metadata, f, indent=2)
#
# print("Log de version créé avec succès!")
# print(f"Dataset final : {df_no_outliers.shape}")