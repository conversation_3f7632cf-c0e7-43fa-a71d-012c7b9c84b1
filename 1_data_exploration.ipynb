# Imports standard
import os
import sqlite3
from datetime import datetime
from pathlib import Path

# Imports tiers
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from scipy import stats

# Configuration des visualisations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Imports locaux - modules utils optimisés
from utils.core import init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR
from utils.data_tools import (
    load_database, get_table_names, load_table,
    describe_missing, plot_missing_heatmap, plot_correlation_matrix,
    detect_outliers_iqr, detect_outliers_zscore, export_artifact
)
from utils.analysis_tools import handle_missing_values, describe_missing_data
from utils.clustering_visualization import export_figure

# Configuration du notebook
init_notebook()
print(f"📊 Notebook d'exploration des données - Projet Olist")
print(f"🎯 Seed fixé à {SEED} pour la reproductibilité")
print(f"📁 Dossier de sortie : {REPORTS_DIR}")

# Chargement des données depuis la base SQLite
data_path = 'data/raw/olist.db'

# Connexion à la base de données
conn = load_database(data_path)

# Exploration des tables disponibles
tables = get_table_names(conn)
print(f"📋 Tables disponibles dans la base : {tables}")

# Chargement des tables principales
customers = load_table(conn, 'customers')
orders = load_table(conn, 'orders')
order_items = load_table(conn, 'order_items')
order_reviews = load_table(conn, 'order_reviews')
products = load_table(conn, 'products')

print(f"\n📊 Dimensions des tables :")
print(f"- Customers: {customers.shape}")
print(f"- Orders: {orders.shape}")
print(f"- Order Items: {order_items.shape}")
print(f"- Order Reviews: {order_reviews.shape}")
print(f"- Products: {products.shape}")

conn.close()

# Création d'une vue consolidée des données client
# Jointure des tables pour créer un dataset d'analyse

# Conversion des dates
orders['order_purchase_timestamp'] = pd.to_datetime(orders['order_purchase_timestamp'])
orders['order_delivered_customer_date'] = pd.to_datetime(orders['order_delivered_customer_date'])

# Calcul des métriques par client
customer_metrics = orders.groupby('customer_id').agg({
    'order_id': 'count',  # Fréquence
    'order_purchase_timestamp': ['min', 'max'],  # Première et dernière commande
}).round(2)

# Aplatissement des colonnes multi-niveaux
customer_metrics.columns = ['frequency', 'first_order_date', 'last_order_date']
customer_metrics = customer_metrics.reset_index()

# Calcul de la récence (jours depuis la dernière commande)
reference_date = orders['order_purchase_timestamp'].max()
customer_metrics['recency'] = (reference_date - customer_metrics['last_order_date']).dt.days

# Jointure avec les informations client
df = customer_metrics.merge(customers[['customer_id', 'customer_state', 'customer_city']],
                           on='customer_id', how='left')

print(f"📊 Dataset consolidé créé : {df.shape}")
print(f"\n🔍 Aperçu des premières lignes :")
display(df.head())

print(f"\n📊 Types de données :")
print(df.dtypes)

# Ajout du montant total par client (métrique Monétaire)
# Calcul du montant total par commande depuis order_items
order_amounts = order_items.groupby('order_id')['price'].sum().reset_index()
order_amounts.columns = ['order_id', 'total_amount']

# Jointure avec les commandes pour avoir customer_id
orders_with_amounts = orders[['order_id', 'customer_id']].merge(order_amounts, on='order_id')

# Calcul du montant total par client
customer_monetary = orders_with_amounts.groupby('customer_id')['total_amount'].sum().reset_index()
customer_monetary.columns = ['customer_id', 'monetary']

# Ajout de la métrique monétaire au dataset principal
df = df.merge(customer_monetary, on='customer_id', how='left')

# Vérification des doublons et de la clé primaire
print(f"🔍 Vérifications de qualité des données :")
print(f"- Nombre de doublons : {df.duplicated().sum()}")
print(f"- Nombre de customer_id uniques : {df['customer_id'].nunique()}")
print(f"- Nombre total de lignes : {len(df)}")

# Vérification de la clé primaire
if df['customer_id'].nunique() == len(df):
    print("✓ customer_id est bien une clé primaire")
else:
    print("⚠️ customer_id n'est pas unique")

print(f"\n💰 Dataset final avec métriques RFM : {df.shape}")
print(f"Colonnes : {list(df.columns)}")
display(df.head())

# Analyse détaillée des valeurs manquantes
missing_analysis = describe_missing_data(df)
print(f"\n📈 Détail des valeurs manquantes par colonne :")
display(missing_analysis)

# Analyse des patterns de valeurs manquantes
if missing_analysis['nb_manquants'].sum() > 0:
    print(f"\n🔍 Colonnes avec valeurs manquantes :")
    cols_with_missing = missing_analysis[missing_analysis['nb_manquants'] > 0]['colonne'].tolist()
    for col in cols_with_missing:
        print(f"- {col}: {missing_analysis[missing_analysis['colonne']==col]['pct_manquants'].iloc[0]:.2f}%")
else:
    print("✓ Aucune valeur manquante détectée dans le dataset !")

# Visualisation heatmap des valeurs manquantes
if missing_analysis['nb_manquants'].sum() > 0:
    fig = plot_missing_heatmap(df, figsize=(12, 8))
    plt.xticks(rotation=45)
    plt.tight_layout()

    # Export de la figure selon les conventions du projet
    export_figure(fig, notebook_name="1",
                 export_number=1, base_name="missing_values_heatmap")
    plt.show()
else:
    print("🎉 Pas de valeurs manquantes à visualiser !")

# Traitement des valeurs manquantes
print(f"🧽 Traitement des valeurs manquantes...")

# Copie de travail pour préserver les données originales
df_clean = df.copy()

# Traitement intelligent des valeurs manquantes
if missing_analysis['nb_manquants'].sum() > 0:
    df_clean = handle_missing_values(df_clean, name="dataset_client", strategy="default")
    print(f"Shape après nettoyage : {df_clean.shape}")

    # Vérification post-traitement
    remaining_missing = df_clean.isnull().sum().sum()
    print(f"Valeurs manquantes restantes : {remaining_missing}")
else:
    print("✓ Aucun traitement nécessaire - dataset déjà complet")

print(f"\n📊 Dataset nettoyé final : {df_clean.shape}")

# Analyse des distributions des variables numériques
numeric_columns = df_clean.select_dtypes(include=[np.number]).columns.tolist()
print(f"📊 Variables numériques à analyser : {numeric_columns}")

# Création des histogrammes pour les variables RFM
rfm_columns = ['recency', 'frequency', 'monetary']
available_rfm = [col for col in rfm_columns if col in df_clean.columns]

if available_rfm:
    fig, axes = plt.subplots(1, len(available_rfm), figsize=(15, 5))
    if len(available_rfm) == 1:
        axes = [axes]

    for i, col in enumerate(available_rfm):
        axes[i].hist(df_clean[col].dropna(), bins=50, alpha=0.7, edgecolor='black', color='skyblue')
        axes[i].set_title(f'Distribution de {col}')
        axes[i].set_xlabel(col)
        axes[i].set_ylabel('Fréquence')
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()

    # Export de la figure
    export_figure(fig, notebook_name="1",
                 export_number=2, base_name="rfm_distributions")
    plt.show()
else:
    print("⚠️ Aucune variable RFM trouvée pour la visualisation")

# Analyse des variables qualitatives
categorical_columns = df_clean.select_dtypes(include=['object']).columns.tolist()
print(f"🏷️ Variables qualitatives : {categorical_columns}")

if categorical_columns:
    # Analyse de la répartition géographique (states et cities)
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))

    # Distribution par état
    if 'customer_state' in df_clean.columns:
        state_counts = df_clean['customer_state'].value_counts().head(10)
        state_counts.plot(kind='bar', ax=axes[0], color='lightcoral')
        axes[0].set_title('Top 10 des États par nombre de clients')
        axes[0].set_xlabel('État')
        axes[0].set_ylabel('Nombre de clients')
        axes[0].tick_params(axis='x', rotation=45)

    # Distribution par ville (top 10)
    if 'customer_city' in df_clean.columns:
        city_counts = df_clean['customer_city'].value_counts().head(10)
        city_counts.plot(kind='bar', ax=axes[1], color='lightgreen')
        axes[1].set_title('Top 10 des Villes par nombre de clients')
        axes[1].set_xlabel('Ville')
        axes[1].set_ylabel('Nombre de clients')
        axes[1].tick_params(axis='x', rotation=45)

    plt.tight_layout()

    # Export de la figure
    export_figure(fig, notebook_name="1",
                 export_number=3, base_name="geographic_distribution")
    plt.show()

    # Statistiques détaillées
    print(f"\n📊 Statistiques géographiques :")
    if 'customer_state' in df_clean.columns:
        print(f"- Nombre d'états uniques : {df_clean['customer_state'].nunique()}")
    if 'customer_city' in df_clean.columns:
        print(f"- Nombre de villes uniques : {df_clean['customer_city'].nunique()}")
else:
    print("ℹ️ Aucune variable qualitative à analyser")

# Statistiques descriptives détaillées
print(f"📊 Statistiques descriptives du dataset :")
desc_stats = df_clean.describe()
display(desc_stats)

# Calcul des mesures de forme (asymétrie et aplatissement)
numeric_df = df_clean.select_dtypes(include=[np.number])
if not numeric_df.empty:
    skewness = numeric_df.skew()
    kurtosis = numeric_df.kurtosis()

    shape_stats = pd.DataFrame({
        'Variable': skewness.index,
        'Skewness': skewness.values,
        'Kurtosis': kurtosis.values
    })

    print(f"\n📈 Mesures de forme (asymétrie et aplatissement) :")
    display(shape_stats)

    # Interprétation des mesures
    print(f"\n🔍 Interprétation :")
    for idx, row in shape_stats.iterrows():
        var_name = row['Variable']
        skew_val = row['Skewness']
        kurt_val = row['Kurtosis']

        # Interprétation de l'asymétrie
        if abs(skew_val) < 0.5:
            skew_interp = "symétrique"
        elif skew_val > 0.5:
            skew_interp = "asymétrie positive (queue à droite)"
        else:
            skew_interp = "asymétrie négative (queue à gauche)"

        print(f"- {var_name}: {skew_interp}")
else:
    print("⚠️ Aucune variable numérique pour les statistiques descriptives")

# Matrice de corrélation des variables numériques
numeric_df = df_clean.select_dtypes(include=[np.number])

if not numeric_df.empty and numeric_df.shape[1] > 1:
    print(f"🔗 Analyse des corrélations entre variables numériques")

    # Utilisation de la fonction optimisée du module data_tools
    fig, correlation_matrix = plot_correlation_matrix(
        numeric_df,
        figsize=(10, 8),
        annot=True,
        mask_upper=True,
        cmap='coolwarm'
    )

    # Export de la figure
    export_figure(fig, notebook_name="1",
                 export_number=4, base_name="correlation_matrix")
    plt.show()

    # Analyse des corrélations fortes
    print(f"\n🔍 Corrélations significatives (|r| > 0.5) :")
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    correlation_matrix_masked = correlation_matrix.mask(mask)

    strong_corr = []
    for i in range(len(correlation_matrix_masked.columns)):
        for j in range(len(correlation_matrix_masked.columns)):
            if not pd.isna(correlation_matrix_masked.iloc[i, j]):
                corr_val = correlation_matrix_masked.iloc[i, j]
                if abs(corr_val) > 0.5:
                    var1 = correlation_matrix_masked.columns[i]
                    var2 = correlation_matrix_masked.columns[j]
                    strong_corr.append((var1, var2, corr_val))

    if strong_corr:
        for var1, var2, corr in strong_corr:
            print(f"- {var1} ↔ {var2}: {corr:.3f}")
    else:
        print("Aucune corrélation forte détectée")

else:
    print("⚠️ Pas assez de variables numériques pour calculer les corrélations")

# Analyse des relations entre variables continues (scatterplots)
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()
rfm_cols = [col for col in ['recency', 'frequency', 'monetary'] if col in numeric_cols]

if len(rfm_cols) >= 2:
    print(f"🔗 Analyse des relations entre variables RFM")

    # Création des scatterplots pour les paires de variables RFM
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))

    # Frequency vs Monetary
    if 'frequency' in rfm_cols and 'monetary' in rfm_cols:
        axes[0].scatter(df_clean['frequency'], df_clean['monetary'], alpha=0.6, color='blue')
        axes[0].set_xlabel('Fréquence d\'achat')
        axes[0].set_ylabel('Montant total (€)')
        axes[0].set_title('Relation Fréquence vs Montant')
        axes[0].grid(True, alpha=0.3)

    # Recency vs Monetary
    if 'recency' in rfm_cols and 'monetary' in rfm_cols:
        axes[1].scatter(df_clean['recency'], df_clean['monetary'], alpha=0.6, color='green')
        axes[1].set_xlabel('Récence (jours)')
        axes[1].set_ylabel('Montant total (€)')
        axes[1].set_title('Relation Récence vs Montant')
        axes[1].grid(True, alpha=0.3)

    # Recency vs Frequency
    if 'recency' in rfm_cols and 'frequency' in rfm_cols:
        axes[2].scatter(df_clean['recency'], df_clean['frequency'], alpha=0.6, color='red')
        axes[2].set_xlabel('Récence (jours)')
        axes[2].set_ylabel('Fréquence d\'achat')
        axes[2].set_title('Relation Récence vs Fréquence')
        axes[2].grid(True, alpha=0.3)

    plt.tight_layout()

    # Export avec la bonne convention de nommage
    export_figure(fig, notebook_name="1",
                 export_number=7, base_name="rfm_scatterplots")
    plt.show()

    # Calcul des corrélations spécifiques
    print(f"\n📈 Corrélations entre variables RFM :")
    for i, var1 in enumerate(rfm_cols):
        for var2 in rfm_cols[i+1:]:
            corr = df_clean[var1].corr(df_clean[var2])
            print(f"- {var1} ↔ {var2}: {corr:.3f}")
else:
    print("⚠️ Pas assez de variables numériques pour l'analyse des relations")

# Analyse des relations entre variables quantitatives et qualitatives
categorical_cols = df_clean.select_dtypes(include=['object']).columns.tolist()
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()

if categorical_cols and numeric_cols:
    print(f"📈 Analyse des relations quantitatif vs qualitatif")

    # Focus sur les variables géographiques vs métriques RFM
    if 'customer_state' in categorical_cols:
        # Sélection des top 10 états pour la lisibilité
        top_states = df_clean['customer_state'].value_counts().head(10).index
        df_top_states = df_clean[df_clean['customer_state'].isin(top_states)]

        # Création des boxplots pour les variables RFM par état
        rfm_vars = [col for col in ['monetary', 'frequency', 'recency'] if col in numeric_cols]

        if rfm_vars:
            fig, axes = plt.subplots(1, len(rfm_vars), figsize=(6*len(rfm_vars), 6))
            if len(rfm_vars) == 1:
                axes = [axes]

            for i, var in enumerate(rfm_vars):
                sns.boxplot(data=df_top_states, x='customer_state', y=var, ax=axes[i])
                axes[i].set_title(f'Distribution de {var} par état')
                axes[i].tick_params(axis='x', rotation=45)
                axes[i].grid(True, alpha=0.3)

            plt.tight_layout()

            # Export avec la bonne convention
            export_figure(fig, notebook_name="1",
                         export_number=8, base_name="rfm_by_state_boxplots")
            plt.show()

            # Statistiques par état
            print(f"\n📊 Statistiques des variables RFM par état (top 5) :")
            for var in rfm_vars[:2]:  # Limiter l'affichage
                print(f"\n--- {var.upper()} par état ---")
                state_stats = df_top_states.groupby('customer_state')[var].agg(['mean', 'median', 'std']).round(2)
                display(state_stats.head())

    # Analyse complémentaire : distribution géographique des segments
    if 'customer_state' in categorical_cols and 'monetary' in numeric_cols:
        print(f"\n🌍 Analyse géographique des profils clients :")

        # Création de catégories de valeur client
        df_clean['value_segment'] = pd.cut(df_clean['monetary'],
                                          bins=3,
                                          labels=['Faible', 'Moyen', 'Elevé'])

        # Tableau croisé état vs segment de valeur
        crosstab = pd.crosstab(df_clean['customer_state'],
                              df_clean['value_segment'],
                              normalize='index') * 100

        print("Répartition des segments de valeur par état (%) :")
        display(crosstab.head(10).round(1))

else:
    print("⚠️ Pas de variables qualitatives ou quantitatives pour l'analyse croisée")

# TODO: Pairplot ciblé sur variables candidates pour segmentation
# # Sélection des variables les plus importantes pour la segmentation
# key_variables = ['recency', 'frequency', 'monetary']  # Variables RFM
#
# if all(var in df_clean.columns for var in key_variables):
#     sns.pairplot(df_clean[key_variables], diag_kind='kde')
#     plt.suptitle('Relations entre variables RFM', y=1.02)
#     plt.tight_layout()
#     plt.show()

# TODO: Préparation ACP si nécessaire
# from sklearn.decomposition import PCA
# from sklearn.preprocessing import StandardScaler
#
# # Standardisation des données pour ACP
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(numeric_df)
#
# # ACP exploratoire
# pca = PCA()
# pca.fit(X_scaled)
#
# # Variance expliquée
# plt.figure(figsize=(10, 6))
# plt.bar(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_)
# plt.xlabel('Composantes principales')
# plt.ylabel('Variance expliquée')
# plt.title('Variance expliquée par composante')
# plt.show()

# Détection des outliers par méthode IQR et Z-score
print(f"🔍 Détection des outliers sur les variables numériques")

outliers_summary = []
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns

for col in numeric_cols:
    if df_clean[col].notna().sum() > 0:  # Vérifier qu'il y a des données non nulles
        print(f"\n--- Analyse des outliers pour '{col}' ---")

        # Méthode IQR
        try:
            outliers_iqr, bounds = detect_outliers_iqr(df_clean, col)
            iqr_count = len(outliers_iqr)
            iqr_pct = (iqr_count / len(df_clean)) * 100
        except Exception as e:
            print(f"Erreur IQR pour {col}: {e}")
            iqr_count, iqr_pct = 0, 0

        # Méthode Z-score
        try:
            outliers_zscore = detect_outliers_zscore(df_clean, col, threshold=3.0)
            zscore_count = len(outliers_zscore)
            zscore_pct = (zscore_count / len(df_clean)) * 100
        except Exception as e:
            print(f"Erreur Z-score pour {col}: {e}")
            zscore_count, zscore_pct = 0, 0

        # Stockage des résultats
        outliers_summary.append({
            'Variable': col,
            'IQR_outliers': iqr_count,
            'IQR_pct': iqr_pct,
            'Zscore_outliers': zscore_count,
            'Zscore_pct': zscore_pct
        })

# Résumé des outliers
if outliers_summary:
    outliers_df = pd.DataFrame(outliers_summary)
    print(f"\n📈 Résumé de la détection d'outliers :")
    display(outliers_df)
else:
    print("⚠️ Aucune variable numérique valide pour la détection d'outliers")

# Visualisation des outliers avec boxplots
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns

if len(numeric_cols) > 0:
    # Calcul du nombre de lignes et colonnes pour les subplots
    n_cols = min(3, len(numeric_cols))
    n_rows = (len(numeric_cols) + n_cols - 1) // n_cols

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))

    # Gestion du cas où il n'y a qu'une seule variable
    if len(numeric_cols) == 1:
        axes = [axes]
    elif n_rows == 1:
        axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]
    else:
        axes = axes.flatten()

    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            sns.boxplot(y=df_clean[col], ax=axes[i], color='lightblue')
            axes[i].set_title(f'Boxplot de {col}')
            axes[i].grid(True, alpha=0.3)

    # Masquer les axes inutilisés
    for i in range(len(numeric_cols), len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()

    # Export de la figure
    export_figure(fig, notebook_name="1",
                 export_number=5, base_name="outliers_boxplots")
    plt.show()
else:
    print("⚠️ Aucune variable numérique à visualiser")

# Traitement des outliers selon pertinence métier
print(f"🔧 Décision de traitement des outliers")

# Analyse contextuelle des outliers pour la segmentation client
print(f"\n📋 Analyse contextuelle des outliers :")
print(f"- Les outliers en e-commerce peuvent représenter des clients VIP ou des comportements exceptionnels")
print(f"- Pour la segmentation client, ces profils atypiques sont souvent précieux")
print(f"- Décision : Conservation des outliers pour préserver la diversité des segments")

# Création d'une copie pour le traitement (ici, on garde tous les outliers)
df_final = df_clean.copy()

# Optionnel : Marquage des outliers pour analyse future
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns
outlier_flags = pd.DataFrame(index=df_clean.index)

for col in numeric_cols:
    if df_clean[col].notna().sum() > 0:
        # Marquage des outliers IQR
        Q1 = df_clean[col].quantile(0.25)
        Q3 = df_clean[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        outlier_flags[f'{col}_outlier'] = (
            (df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)
        )

# Ajout des flags d'outliers au dataset final
df_final = pd.concat([df_final, outlier_flags], axis=1)

# Statistiques des outliers marqués
outlier_cols = [col for col in df_final.columns if col.endswith('_outlier')]
if outlier_cols:
    print(f"\n🏷️ Marquage des outliers effectué :")
    for col in outlier_cols:
        outlier_count = df_final[col].sum()
        outlier_pct = (outlier_count / len(df_final)) * 100
        print(f"- {col}: {outlier_count} outliers ({outlier_pct:.1f}%)")

print(f"\n✅ Dataset final conservé avec outliers : {df_final.shape}")
print(f"Justification : Les outliers en segmentation client représentent souvent des segments de haute valeur")

# Calcul de l'ancienneté client et métriques temporelles
print(f"🕰️ Calcul des métriques temporelles")

# Calcul de l'ancienneté client (jours depuis la première commande)
if 'first_order_date' in df_final.columns:
    reference_date = df_final['last_order_date'].max()
    df_final['customer_age_days'] = (reference_date - df_final['first_order_date']).dt.days

    print(f"Ancienneté client calculée :")
    print(f"- Ancienneté moyenne : {df_clean['customer_age_days'].mean():.0f} jours")
    print(f"- Ancienneté médiane : {df_clean['customer_age_days'].median():.0f} jours")
    print(f"- Ancienneté min : {df_clean['customer_age_days'].min():.0f} jours")
    print(f"- Ancienneté max : {df_clean['customer_age_days'].max():.0f} jours")

    # Création de catégories d'ancienneté
    df_clean['customer_age_category'] = pd.cut(
        df_clean['customer_age_days'],
        bins=[0, 30, 90, 180, 365, float('inf')],
        labels=['Nouveau (<30j)', 'Récent (30-90j)', 'Etabli (90-180j)',
               'Ancien (180-365j)', 'Très ancien (>365j)']
    )

    # Distribution des catégories d'ancienneté
    age_distribution = df_clean['customer_age_category'].value_counts()
    print(f"\n📈 Distribution des catégories d'ancienneté :")
    for category, count in age_distribution.items():
        pct = (count / len(df_clean)) * 100
        print(f"- {category}: {count} clients ({pct:.1f}%)")

else:
    print("⚠️ Colonnes de dates manquantes pour le calcul d'ancienneté")

# Calcul des métriques d'achat et d'activité
print(f"🛒 Calcul des métriques d'achat et d'activité")

# Calcul du panier moyen
if 'monetary' in df_clean.columns and 'frequency' in df_clean.columns:
    df_clean['average_basket'] = df_clean['monetary'] / df_clean['frequency']

    print(f"Métriques d'achat calculées :")
    print(f"- Panier moyen : {df_clean['average_basket'].mean():.2f} €")
    print(f"- Panier médian : {df_clean['average_basket'].median():.2f} €")
    print(f"- Fréquence moyenne : {df_clean['frequency'].mean():.2f} commandes")
    print(f"- Montant moyen par client : {df_clean['monetary'].mean():.2f} €")

    # Calcul de métriques d'activité supplémentaires
    if 'customer_age_days' in df_clean.columns:
        # Fréquence d'achat normalisée (commandes par mois)
        df_clean['frequency_per_month'] = (df_clean['frequency'] / (df_clean['customer_age_days'] / 30.44)).round(2)

        # Montant par mois d'activité
        df_clean['monetary_per_month'] = (df_clean['monetary'] / (df_clean['customer_age_days'] / 30.44)).round(2)

        print(f"\n📊 Métriques d'activité normalisées :")
        print(f"- Fréquence par mois : {df_clean['frequency_per_month'].mean():.2f} commandes/mois")
        print(f"- Montant par mois : {df_clean['monetary_per_month'].mean():.2f} €/mois")

    # Création de segments de valeur de panier
    df_clean['basket_segment'] = pd.cut(
        df_clean['average_basket'],
        bins=3,
        labels=['Petit panier', 'Panier moyen', 'Gros panier']
    )

    # Distribution des segments de panier
    basket_distribution = df_clean['basket_segment'].value_counts()
    print(f"\n🛍️ Distribution des segments de panier :")
    for segment, count in basket_distribution.items():
        pct = (count / len(df_clean)) * 100
        print(f"- {segment}: {count} clients ({pct:.1f}%)")

else:
    print("⚠️ Variables monetary ou frequency manquantes pour le calcul des métriques")

# Conversion et traitement des dates - Extraction de features temporelles
print(f"📅 Extraction de features temporelles")

# Identification des colonnes de dates
date_columns = df_clean.select_dtypes(include=['datetime64']).columns
print(f"Colonnes de dates trouvées : {list(date_columns)}")

# Extraction de features temporelles pour les dates de commande
if 'first_order_date' in df_clean.columns:
    # Extraction des composantes temporelles de la première commande
    df_clean['first_order_year'] = df_clean['first_order_date'].dt.year
    df_clean['first_order_month'] = df_clean['first_order_date'].dt.month
    df_clean['first_order_weekday'] = df_clean['first_order_date'].dt.weekday
    df_clean['first_order_quarter'] = df_clean['first_order_date'].dt.quarter

    print(f"Features temporelles extraites pour first_order_date")

    # Analyse de la saisonnalité des premières commandes
    print(f"\n📊 Analyse de saisonnalité des premières commandes :")

    # Distribution par mois
    month_dist = df_clean['first_order_month'].value_counts().sort_index()
    print(f"\nDistribution par mois :")
    month_names = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
                   'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc']
    for month, count in month_dist.items():
        pct = (count / len(df_clean)) * 100
        print(f"- {month_names[int(month)-1]}: {count} clients ({pct:.1f}%)")

    # Distribution par jour de la semaine
    weekday_dist = df_clean['first_order_weekday'].value_counts().sort_index()
    print(f"\nDistribution par jour de la semaine :")
    weekday_names = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
    for weekday, count in weekday_dist.items():
        pct = (count / len(df_clean)) * 100
        print(f"- {weekday_names[weekday]}: {count} clients ({pct:.1f}%)")

    # Distribution par trimestre
    quarter_dist = df_clean['first_order_quarter'].value_counts().sort_index()
    print(f"\nDistribution par trimestre :")
    for quarter, count in quarter_dist.items():
        pct = (count / len(df_clean)) * 100
        print(f"- Q{quarter}: {count} clients ({pct:.1f}%)")

else:
    print("⚠️ Aucune colonne de date disponible pour l'extraction de features")

# Résumé des nouvelles variables créées
print(f"\n✅ Résumé des variables créées dans cette section :")
new_vars = [col for col in df_clean.columns if any(x in col for x in
           ['age_', 'basket', 'per_month', 'order_year', 'order_month', 'order_weekday', 'order_quarter'])]
print(f"Variables ajoutées : {new_vars}")
print(f"Dataset final : {df_clean.shape}")

# Sauvegarde du dataset nettoyé et exploré
from utils.save_load import save_results

# Création des dossiers de sortie
os.makedirs('data/processed', exist_ok=True)

# Export CSV avec convention de nommage du projet
csv_path = 'data/processed/1_01_cleaned_dataset.csv'
df_final.to_csv(csv_path, index=False)
print(f"💾 Dataset CSV sauvegardé : {csv_path}")

# Export pickle pour préserver les types de données
pickle_path = 'data/processed/1_01_cleaned_dataset.pkl'
df_final.to_pickle(pickle_path)
print(f"💾 Dataset pickle sauvegardé : {pickle_path}")

# Sauvegarde des métadonnees avec la fonction utils
metadata = {
    'dataset_name': 'cleaned_customer_data',
    'shape': df_final.shape,
    'columns': list(df_final.columns),
    'dtypes': df_clean.dtypes.to_dict(),
    'processing_steps': [
        'Chargement depuis SQLite',
        'Création métriques RFM',
        'Jointure tables clients',
        'Nettoyage valeurs manquantes',
        'Analyse exploratoire complète'
    ]
}

save_results(metadata, notebook_name="1",
            export_number=6, base_name="dataset_metadata")

print(f"\n✓ Sauvegarde complète du dataset nettoyé : {df_clean.shape}")

# Création du log de version et métadonnées détaillées
import json
from datetime import datetime

print(f"📋 Création du log de version et métadonnées détaillées")

# Calcul des statistiques de traitement
original_shape = df.shape if 'df' in locals() else df_final.shape
final_shape = df_final.shape

# Métadonnées complètes du traitement
processing_log = {
    'processing_info': {
        'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'notebook_version': '1.0',
        'python_version': '3.13.2'
    },
    'data_transformation': {
        'original_shape': original_shape,
        'final_shape': final_shape
    },
    'data_quality': {
        'missing_values_handled': True,
        'outliers_detected': True,
        'outliers_removed': False,
        'outliers_flagged': True
    },
    'feature_engineering': {
        'rfm_metrics_created': True,
        'temporal_features_extracted': True,
        'customer_segmentation_prepared': True
    },
    'exports_created': {
        'cleaned_dataset_csv': 'data/processed/1_01_cleaned_dataset.csv',
        'cleaned_dataset_pickle': 'data/processed/1_01_cleaned_dataset.pkl',
        'figures_exported': 12
    }
}

# Sauvegarde du log de version
log_path = 'data/processed/1_processing_log.json'
with open(log_path, 'w', encoding='utf-8') as f:
    json.dump(processing_log, f, indent=2, ensure_ascii=False, default=str)

print(f"✅ Log de version créé avec succès : {log_path}")
print(f"📊 Dataset final : {df_final.shape}")