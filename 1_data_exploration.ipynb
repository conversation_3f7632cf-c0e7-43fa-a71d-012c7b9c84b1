# Imports standard
import os
import sqlite3
from datetime import datetime
from pathlib import Path

# Imports tiers
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from scipy import stats

# Configuration des visualisations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Imports locaux - modules utils optimisés
from utils.core import init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR
from utils.data_tools import (
    load_database, get_table_names, load_table,
    describe_missing, plot_missing_heatmap, plot_correlation_matrix,
    detect_outliers_iqr, detect_outliers_zscore, export_artifact
)
from utils.analysis_tools import handle_missing_values, describe_missing_data
from utils.clustering_visualization import export_figure

# Configuration du notebook
init_notebook()
print(f"📊 Notebook d'exploration des données - Projet Olist")
print(f"🎯 Seed fixé à {SEED} pour la reproductibilité")
print(f"📁 Dossier de sortie : {REPORTS_DIR}")

# Chargement des données depuis la base SQLite
data_path = 'data/raw/olist.db'

# Connexion à la base de données
conn = load_database(data_path)

# Exploration des tables disponibles
tables = get_table_names(conn)
print(f"📋 Tables disponibles dans la base : {tables}")

# Chargement des tables principales
customers = load_table(conn, 'customers')
orders = load_table(conn, 'orders')
order_items = load_table(conn, 'order_items')
order_reviews = load_table(conn, 'order_reviews')
products = load_table(conn, 'products')

print(f"\n📊 Dimensions des tables :")
print(f"- Customers: {customers.shape}")
print(f"- Orders: {orders.shape}")
print(f"- Order Items: {order_items.shape}")
print(f"- Order Reviews: {order_reviews.shape}")
print(f"- Products: {products.shape}")

conn.close()

# Création d'une vue consolidée des données client
# Jointure des tables pour créer un dataset d'analyse

# Conversion des dates
orders['order_purchase_timestamp'] = pd.to_datetime(orders['order_purchase_timestamp'])
orders['order_delivered_customer_date'] = pd.to_datetime(orders['order_delivered_customer_date'])

# Calcul des métriques par client
customer_metrics = orders.groupby('customer_id').agg({
    'order_id': 'count',  # Fréquence
    'order_purchase_timestamp': ['min', 'max'],  # Première et dernière commande
}).round(2)

# Aplatissement des colonnes multi-niveaux
customer_metrics.columns = ['frequency', 'first_order_date', 'last_order_date']
customer_metrics = customer_metrics.reset_index()

# Calcul de la récence (jours depuis la dernière commande)
reference_date = orders['order_purchase_timestamp'].max()
customer_metrics['recency'] = (reference_date - customer_metrics['last_order_date']).dt.days

# Jointure avec les informations client
df = customer_metrics.merge(customers[['customer_id', 'customer_state', 'customer_city']],
                           on='customer_id', how='left')

print(f"📊 Dataset consolidé créé : {df.shape}")
print(f"\n🔍 Aperçu des premières lignes :")
display(df.head())

print(f"\n📊 Types de données :")
print(df.dtypes)

# Ajout du montant total par client (métrique Monétaire)
# Calcul du montant total par commande depuis order_items
order_amounts = order_items.groupby('order_id')['price'].sum().reset_index()
order_amounts.columns = ['order_id', 'total_amount']

# Jointure avec les commandes pour avoir customer_id
orders_with_amounts = orders[['order_id', 'customer_id']].merge(order_amounts, on='order_id')

# Calcul du montant total par client
customer_monetary = orders_with_amounts.groupby('customer_id')['total_amount'].sum().reset_index()
customer_monetary.columns = ['customer_id', 'monetary']

# Ajout de la métrique monétaire au dataset principal
df = df.merge(customer_monetary, on='customer_id', how='left')

# Vérification des doublons et de la clé primaire
print(f"🔍 Vérifications de qualité des données :")
print(f"- Nombre de doublons : {df.duplicated().sum()}")
print(f"- Nombre de customer_id uniques : {df['customer_id'].nunique()}")
print(f"- Nombre total de lignes : {len(df)}")

# Vérification de la clé primaire
if df['customer_id'].nunique() == len(df):
    print("✓ customer_id est bien une clé primaire")
else:
    print("⚠️ customer_id n'est pas unique")

print(f"\n💰 Dataset final avec métriques RFM : {df.shape}")
print(f"Colonnes : {list(df.columns)}")
display(df.head())

# Analyse détaillée des valeurs manquantes
missing_analysis = describe_missing_data(df)
print(f"\n📈 Détail des valeurs manquantes par colonne :")
display(missing_analysis)

# Analyse des patterns de valeurs manquantes
if missing_analysis['nb_manquants'].sum() > 0:
    print(f"\n🔍 Colonnes avec valeurs manquantes :")
    cols_with_missing = missing_analysis[missing_analysis['nb_manquants'] > 0]['colonne'].tolist()
    for col in cols_with_missing:
        print(f"- {col}: {missing_analysis[missing_analysis['colonne']==col]['pct_manquants'].iloc[0]:.2f}%")
else:
    print("✓ Aucune valeur manquante détectée dans le dataset !")

# Visualisation heatmap des valeurs manquantes
if missing_analysis['nb_manquants'].sum() > 0:
    fig = plot_missing_heatmap(df, figsize=(12, 8))
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Export de la figure selon les conventions du projet
    export_figure(fig, notebook_name="1_data_exploration", 
                 export_number=1, base_name="missing_values_heatmap")
    plt.show()
else:
    print("🎉 Pas de valeurs manquantes à visualiser !")

# Traitement des valeurs manquantes
print(f"🧽 Traitement des valeurs manquantes...")

# Copie de travail pour préserver les données originales
df_clean = df.copy()

# Traitement intelligent des valeurs manquantes
if missing_analysis['nb_manquants'].sum() > 0:
    df_clean = handle_missing_values(df_clean, name="dataset_client", strategy="default")
    print(f"Shape après nettoyage : {df_clean.shape}")
    
    # Vérification post-traitement
    remaining_missing = df_clean.isnull().sum().sum()
    print(f"Valeurs manquantes restantes : {remaining_missing}")
else:
    print("✓ Aucun traitement nécessaire - dataset déjà complet")

print(f"\n📊 Dataset nettoyé final : {df_clean.shape}")

# Analyse des distributions des variables numériques
numeric_columns = df_clean.select_dtypes(include=[np.number]).columns.tolist()
print(f"📊 Variables numériques à analyser : {numeric_columns}")

# Création des histogrammes pour les variables RFM
rfm_columns = ['recency', 'frequency', 'monetary']
available_rfm = [col for col in rfm_columns if col in df_clean.columns]

if available_rfm:
    fig, axes = plt.subplots(1, len(available_rfm), figsize=(15, 5))
    if len(available_rfm) == 1:
        axes = [axes]
    
    for i, col in enumerate(available_rfm):
        axes[i].hist(df_clean[col].dropna(), bins=50, alpha=0.7, edgecolor='black', color='skyblue')
        axes[i].set_title(f'Distribution de {col}')
        axes[i].set_xlabel(col)
        axes[i].set_ylabel('Fréquence')
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Export de la figure
    export_figure(fig, notebook_name="1_data_exploration", 
                 export_number=2, base_name="rfm_distributions")
    plt.show()
else:
    print("⚠️ Aucune variable RFM trouvée pour la visualisation")

# Analyse des variables qualitatives
categorical_columns = df_clean.select_dtypes(include=['object']).columns.tolist()
print(f"🏷️ Variables qualitatives : {categorical_columns}")

if categorical_columns:
    # Analyse de la répartition géographique (states et cities)
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Distribution par état
    if 'customer_state' in df_clean.columns:
        state_counts = df_clean['customer_state'].value_counts().head(10)
        state_counts.plot(kind='bar', ax=axes[0], color='lightcoral')
        axes[0].set_title('Top 10 des États par nombre de clients')
        axes[0].set_xlabel('État')
        axes[0].set_ylabel('Nombre de clients')
        axes[0].tick_params(axis='x', rotation=45)
    
    # Distribution par ville (top 10)
    if 'customer_city' in df_clean.columns:
        city_counts = df_clean['customer_city'].value_counts().head(10)
        city_counts.plot(kind='bar', ax=axes[1], color='lightgreen')
        axes[1].set_title('Top 10 des Villes par nombre de clients')
        axes[1].set_xlabel('Ville')
        axes[1].set_ylabel('Nombre de clients')
        axes[1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # Export de la figure
    export_figure(fig, notebook_name="1_data_exploration", 
                 export_number=3, base_name="geographic_distribution")
    plt.show()
    
    # Statistiques détaillées
    print(f"\n📊 Statistiques géographiques :")
    if 'customer_state' in df_clean.columns:
        print(f"- Nombre d'états uniques : {df_clean['customer_state'].nunique()}")
    if 'customer_city' in df_clean.columns:
        print(f"- Nombre de villes uniques : {df_clean['customer_city'].nunique()}")
else:
    print("ℹ️ Aucune variable qualitative à analyser")

# Statistiques descriptives détaillées
print(f"📊 Statistiques descriptives du dataset :")
desc_stats = df_clean.describe()
display(desc_stats)

# Calcul des mesures de forme (asymétrie et aplatissement)
numeric_df = df_clean.select_dtypes(include=[np.number])
if not numeric_df.empty:
    skewness = numeric_df.skew()
    kurtosis = numeric_df.kurtosis()
    
    shape_stats = pd.DataFrame({
        'Variable': skewness.index,
        'Skewness': skewness.values,
        'Kurtosis': kurtosis.values
    })
    
    print(f"\n📈 Mesures de forme (asymétrie et aplatissement) :")
    display(shape_stats)
    
    # Interprétation des mesures
    print(f"\n🔍 Interprétation :")
    for idx, row in shape_stats.iterrows():
        var_name = row['Variable']
        skew_val = row['Skewness']
        kurt_val = row['Kurtosis']
        
        # Interprétation de l'asymétrie
        if abs(skew_val) < 0.5:
            skew_interp = "symétrique"
        elif skew_val > 0.5:
            skew_interp = "asymétrie positive (queue à droite)"
        else:
            skew_interp = "asymétrie négative (queue à gauche)"
        
        print(f"- {var_name}: {skew_interp}")
else:
    print("⚠️ Aucune variable numérique pour les statistiques descriptives")

# Matrice de corrélation des variables numériques
numeric_df = df_clean.select_dtypes(include=[np.number])

if not numeric_df.empty and numeric_df.shape[1] > 1:
    print(f"🔗 Analyse des corrélations entre variables numériques")
    
    # Utilisation de la fonction optimisée du module data_tools
    fig, correlation_matrix = plot_correlation_matrix(
        numeric_df, 
        figsize=(10, 8), 
        annot=True, 
        mask_upper=True,
        cmap='coolwarm'
    )
    
    # Export de la figure
    export_figure(fig, notebook_name="1_data_exploration", 
                 export_number=4, base_name="correlation_matrix")
    plt.show()
    
    # Analyse des corrélations fortes
    print(f"\n🔍 Corrélations significatives (|r| > 0.5) :")
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    correlation_matrix_masked = correlation_matrix.mask(mask)
    
    strong_corr = []
    for i in range(len(correlation_matrix_masked.columns)):
        for j in range(len(correlation_matrix_masked.columns)):
            if not pd.isna(correlation_matrix_masked.iloc[i, j]):
                corr_val = correlation_matrix_masked.iloc[i, j]
                if abs(corr_val) > 0.5:
                    var1 = correlation_matrix_masked.columns[i]
                    var2 = correlation_matrix_masked.columns[j]
                    strong_corr.append((var1, var2, corr_val))
    
    if strong_corr:
        for var1, var2, corr in strong_corr:
            print(f"- {var1} ↔ {var2}: {corr:.3f}")
    else:
        print("Aucune corrélation forte détectée")
        
else:
    print("⚠️ Pas assez de variables numériques pour calculer les corrélations")

# TODO: Scatterplots pour relations importantes
# # Exemple : relation entre montant et fréquence
# if 'total_amount' in df_clean.columns and 'frequency' in df_clean.columns:
#     plt.figure(figsize=(10, 6))
#     plt.scatter(df_clean['frequency'], df_clean['total_amount'], alpha=0.6)
#     plt.xlabel('Fréquence d\'achat')
#     plt.ylabel('Montant total')
#     plt.title('Relation entre fréquence et montant')
#     plt.show()

# TODO: Boxplots entre variables quantitatives et qualitatives
# # Exemple : montant par catégorie
# if 'category' in df_clean.columns and 'total_amount' in df_clean.columns:
#     plt.figure(figsize=(12, 6))
#     sns.boxplot(data=df_clean, x='category', y='total_amount')
#     plt.title('Distribution du montant par catégorie')
#     plt.xticks(rotation=45)
#     plt.tight_layout()
#     plt.show()

# TODO: Pairplot ciblé sur variables candidates pour segmentation
# # Sélection des variables les plus importantes pour la segmentation
# key_variables = ['recency', 'frequency', 'monetary']  # Variables RFM
#
# if all(var in df_clean.columns for var in key_variables):
#     sns.pairplot(df_clean[key_variables], diag_kind='kde')
#     plt.suptitle('Relations entre variables RFM', y=1.02)
#     plt.tight_layout()
#     plt.show()

# TODO: Préparation ACP si nécessaire
# from sklearn.decomposition import PCA
# from sklearn.preprocessing import StandardScaler
#
# # Standardisation des données pour ACP
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(numeric_df)
#
# # ACP exploratoire
# pca = PCA()
# pca.fit(X_scaled)
#
# # Variance expliquée
# plt.figure(figsize=(10, 6))
# plt.bar(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_)
# plt.xlabel('Composantes principales')
# plt.ylabel('Variance expliquée')
# plt.title('Variance expliquée par composante')
# plt.show()

# Détection des outliers par méthode IQR et Z-score
print(f"🔍 Détection des outliers sur les variables numériques")

outliers_summary = []
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns

for col in numeric_cols:
    if df_clean[col].notna().sum() > 0:  # Vérifier qu'il y a des données non nulles
        print(f"\n--- Analyse des outliers pour '{col}' ---")
        
        # Méthode IQR
        try:
            outliers_iqr, bounds = detect_outliers_iqr(df_clean, col)
            iqr_count = len(outliers_iqr)
            iqr_pct = (iqr_count / len(df_clean)) * 100
        except Exception as e:
            print(f"Erreur IQR pour {col}: {e}")
            iqr_count, iqr_pct = 0, 0
        
        # Méthode Z-score
        try:
            outliers_zscore = detect_outliers_zscore(df_clean, col, threshold=3.0)
            zscore_count = len(outliers_zscore)
            zscore_pct = (zscore_count / len(df_clean)) * 100
        except Exception as e:
            print(f"Erreur Z-score pour {col}: {e}")
            zscore_count, zscore_pct = 0, 0
        
        # Stockage des résultats
        outliers_summary.append({
            'Variable': col,
            'IQR_outliers': iqr_count,
            'IQR_pct': iqr_pct,
            'Zscore_outliers': zscore_count,
            'Zscore_pct': zscore_pct
        })

# Résumé des outliers
if outliers_summary:
    outliers_df = pd.DataFrame(outliers_summary)
    print(f"\n📈 Résumé de la détection d'outliers :")
    display(outliers_df)
else:
    print("⚠️ Aucune variable numérique valide pour la détection d'outliers")

# Visualisation des outliers avec boxplots
numeric_cols = df_clean.select_dtypes(include=[np.number]).columns

if len(numeric_cols) > 0:
    # Calcul du nombre de lignes et colonnes pour les subplots
    n_cols = min(3, len(numeric_cols))
    n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
    
    # Gestion du cas où il n'y a qu'une seule variable
    if len(numeric_cols) == 1:
        axes = [axes]
    elif n_rows == 1:
        axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]
    else:
        axes = axes.flatten()
    
    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            sns.boxplot(y=df_clean[col], ax=axes[i], color='lightblue')
            axes[i].set_title(f'Boxplot de {col}')
            axes[i].grid(True, alpha=0.3)
    
    # Masquer les axes inutilisés
    for i in range(len(numeric_cols), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    # Export de la figure
    export_figure(fig, notebook_name="1_data_exploration", 
                 export_number=5, base_name="outliers_boxplots")
    plt.show()
else:
    print("⚠️ Aucune variable numérique à visualiser")

# TODO: Traitement des outliers selon pertinence métier
# # Définir les seuils selon le contexte business
# df_no_outliers = df_clean.copy()
#
# # Exemple : suppression des outliers extrêmes (Z-score > 3)
# from scipy import stats
# for col in numeric_cols:
#     z_scores = np.abs(stats.zscore(df_clean[col].dropna()))
#     df_no_outliers = df_no_outliers[z_scores < 3]
#
# print(f"Dataset original : {df_clean.shape}")
# print(f"Dataset sans outliers extrêmes : {df_no_outliers.shape}")
# print(f"Suppression de {df_clean.shape[0] - df_no_outliers.shape[0]} lignes")

# TODO: Calcul de l'ancienneté client
# from utils.feature_engineering import calculate_customer_age
#
# # Si on a une colonne de date d'inscription
# if 'first_purchase_date' in df_no_outliers.columns:
#     df_no_outliers['customer_age_days'] = calculate_customer_age(
#         df_no_outliers['first_purchase_date']
#     )
#     print(f"Ancienneté moyenne : {df_no_outliers['customer_age_days'].mean():.0f} jours")

# TODO: Calcul des métriques d'achat
# # Taux d'achat, panier moyen, etc.
# if 'total_amount' in df_no_outliers.columns and 'frequency' in df_no_outliers.columns:
#     df_no_outliers['average_basket'] = df_no_outliers['total_amount'] / df_no_outliers['frequency']
#
#     print("Métriques calculées :")
#     print(f"- Panier moyen : {df_no_outliers['average_basket'].mean():.2f}")
#     print(f"- Fréquence moyenne : {df_no_outliers['frequency'].mean():.2f}")

# TODO: Conversion des colonnes de dates
# date_columns = df_no_outliers.select_dtypes(include=['datetime64']).columns
# print(f"Colonnes de dates trouvées : {list(date_columns)}")
#
# # Extraction de features temporelles si nécessaire
# for col in date_columns:
#     df_no_outliers[f'{col}_year'] = df_no_outliers[col].dt.year
#     df_no_outliers[f'{col}_month'] = df_no_outliers[col].dt.month
#     df_no_outliers[f'{col}_weekday'] = df_no_outliers[col].dt.weekday

# Sauvegarde du dataset nettoyé et exploré
from utils.save_load import save_results

# Création des dossiers de sortie
os.makedirs('data/processed', exist_ok=True)

# Export CSV avec convention de nommage du projet
csv_path = 'data/processed/1_data_exploration_01_cleaned_dataset.csv'
df_clean.to_csv(csv_path, index=False)
print(f"💾 Dataset CSV sauvegardé : {csv_path}")

# Export pickle pour préserver les types de données
pickle_path = 'data/processed/1_data_exploration_01_cleaned_dataset.pkl'
df_clean.to_pickle(pickle_path)
print(f"💾 Dataset pickle sauvegardé : {pickle_path}")

# Sauvegarde des métadonnees avec la fonction utils
metadata = {
    'dataset_name': 'cleaned_customer_data',
    'shape': df_clean.shape,
    'columns': list(df_clean.columns),
    'dtypes': df_clean.dtypes.to_dict(),
    'processing_steps': [
        'Chargement depuis SQLite',
        'Création métriques RFM',
        'Jointure tables clients',
        'Nettoyage valeurs manquantes',
        'Analyse exploratoire complète'
    ]
}

save_results(metadata, notebook_name="1_data_exploration", 
            export_number=6, base_name="dataset_metadata")

print(f"\n✓ Sauvegarde complète du dataset nettoyé : {df_clean.shape}")

# TODO: Création du log de version
# metadata = {
#     'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
#     'original_shape': df.shape,
#     'cleaned_shape': df_no_outliers.shape,
#     'columns': list(df_no_outliers.columns),
#     'missing_values_handled': True,
#     'outliers_removed': True,
#     'feature_engineering': 'basic'
# }
#
# import json
# with open('data/processed/cleaning_log.json', 'w') as f:
#     json.dump(metadata, f, indent=2)
#
# print("Log de version créé avec succès!")
# print(f"Dataset final : {df_no_outliers.shape}")