{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 2 : Feature Engineering (RFM & comportements)\n", "\n", "## Objectif\n", "Construire des variables pertinentes pour la segmentation client. Utiliser le modèle RFM (Récence, Fréquence, Montant), compléter avec d'autres variables comportementales, et préparer les données pour le clustering.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données nettoyées\n", "\n", "### 1.1 Import des librairies nécessaires"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import des librairies\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "\n", "# Preprocessing\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Configuration\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import des utilitaires du projet\n", "from utils.notebook_init import setup_notebook\n", "from utils.feature_engineering import (\n", "    calculate_rfm_metrics,\n", "    calculate_customer_lifetime_features,\n", "    create_behavioral_features\n", ")\n", "from utils.data_tools import load_data\n", "from utils.save_load import save_dataframe, load_dataframe\n", "from utils.visualization_optimizer import plot_feature_distributions\n", "\n", "# Configuration du notebook\n", "setup_notebook()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du fichier nettoyé produit par le Notebook 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données nettoyées du Notebook 1\n", "data_path = 'data/processed/cleaned_data.pkl'\n", "\n", "# TODO: Charger le dataset nettoyé\n", "# df_clean = load_dataframe(data_path)\n", "# print(f\"Dataset chargé depuis : {data_path}\")\n", "# print(f\"Shape : {df_clean.shape}\")\n", "# print(f\"Colonnes : {list(df_clean.columns)}\")\n", "\n", "# Vérification de l'intégrité\n", "# print(f\"\\nVérifications :\")\n", "# print(f\"- Valeurs manquantes : {df_clean.isnull().sum().sum()}\")\n", "# print(f\"- Doublons : {df_clean.duplicated().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérifications d'usage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Vérifications d'usage\n", "# # Vérification des colonnes nécessaires pour RFM\n", "# required_columns = ['customer_id', 'order_date', 'order_value']\n", "# missing_columns = [col for col in required_columns if col not in df_clean.columns]\n", "#\n", "# if missing_columns:\n", "#     print(f\"⚠️ Colonnes manquantes pour RFM : {missing_columns}\")\n", "# else:\n", "#     print(\"✅ Toutes les colonnes nécessaires sont présentes\")\n", "#\n", "# # Aper<PERSON>u des données\n", "# display(df_clean.head())\n", "# display(df_clean.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Calcul des variables RFM\n", "\n", "### 2.1 Définition de la date de référence pour la récence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Définir la date de référence pour calculer la récence\n", "# # Date de référence = dernière date d'achat + 1 jour\n", "# reference_date = df_clean['order_date'].max() + <PERSON><PERSON><PERSON>(days=1)\n", "# print(f\"Date de référence pour la récence : {reference_date}\")\n", "# print(f\"Période d'analyse : du {df_clean['order_date'].min()} au {df_clean['order_date'].max()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Agrégation par client : calcul R, F, M"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul des métriques RFM par client\n", "# rfm_df = calculate_rfm_metrics(\n", "#     df_clean,\n", "#     customer_id='customer_id',\n", "#     order_date='order_date',\n", "#     order_value='order_value',\n", "#     reference_date=reference_date\n", "# )\n", "#\n", "# # <PERSON><PERSON>thode manuelle si la fonction utilitaire n'est pas prête\n", "# rfm_manual = df_clean.groupby('customer_id').agg({\n", "#     'order_date': lambda x: (reference_date - x.max()).days,  # Récence\n", "#     'customer_id': 'count',  # Fré<PERSON>\n", "#     'order_value': ['sum', 'mean']  # Montant total et moyen\n", "# }).round(2)\n", "#\n", "# # Reno<PERSON> les colonnes\n", "# rfm_manual.columns = ['recency', 'frequency', 'monetary_total', 'monetary_avg']\n", "# rfm_manual = rfm_manual.reset_index()\n", "#\n", "# print(f\"Données RFM calculées pour {len(rfm_manual)} clients\")\n", "# display(rfm_manual.head())\n", "# display(rfm_manual.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Construction et validation du DataFrame RFM final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Finalisation du DataFrame RFM\n", "# # Vérifications et nettoyage\n", "# print(\"Distribution des variables RFM :\")\n", "#\n", "# # Visualisation des distributions RFM\n", "# fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "#\n", "# # <PERSON><PERSON><PERSON>\n", "# axes[0,0].hist(rfm_manual['recency'], bins=50, alpha=0.7, color='skyblue')\n", "# axes[0,0].set_title('Distribution de la Récence (jours)')\n", "# axes[0,0].set_xlabel('<PERSON><PERSON> depuis dernier achat')\n", "#\n", "# # Fréquence\n", "# axes[0,1].hist(rfm_manual['frequency'], bins=30, alpha=0.7, color='lightgreen')\n", "# axes[0,1].set_title('Distribution de la Fréquence')\n", "# axes[0,1].set_xlabel('Nombre d\\'achats')\n", "#\n", "# # Montant total\n", "# axes[1,0].hist(rfm_manual['monetary_total'], bins=50, alpha=0.7, color='salmon')\n", "# axes[1,0].set_title('Distribution du Montant Total')\n", "# axes[1,0].set_xlabel('Montant total (€)')\n", "#\n", "# # <PERSON><PERSON> moyen\n", "# axes[1,1].hist(rfm_manual['monetary_avg'], bins=50, alpha=0.7, color='gold')\n", "# axes[1,1].set_title('Distribution du Montant Moyen')\n", "# axes[1,1].set_xlabel('<PERSON><PERSON> moyen (€)')\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Enrichissement comportemental\n", "\n", "### 3.1 Calcul de l'ancienneté client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul de l'ancienneté client\n", "# # Ancienneté = différence entre première et dernière commande\n", "# customer_lifetime = df_clean.groupby('customer_id')['order_date'].agg({\n", "#     'first_order': 'min',\n", "#     'last_order': 'max'\n", "# })\n", "#\n", "# customer_lifetime['customer_lifespan_days'] = (\n", "#     customer_lifetime['last_order'] - customer_lifetime['first_order']\n", "# ).dt.days\n", "#\n", "# # Ancienneté depuis la première commande\n", "# customer_lifetime['days_since_first_order'] = (\n", "#     reference_date - customer_lifetime['first_order']\n", "# ).dt.days\n", "#\n", "# print(f\"Ancienneté moyenne : {customer_lifetime['customer_lifespan_days'].mean():.1f} jours\")\n", "# print(f\"Ancienneté médiane : {customer_lifetime['customer_lifespan_days'].median():.1f} jours\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 <PERSON><PERSON><PERSON> entre commandes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: <PERSON><PERSON><PERSON> du délai moyen entre commandes\n", "# def calculate_avg_order_interval(group):\n", "#     if len(group) <= 1:\n", "#         return np.nan\n", "#     dates = group.sort_values()\n", "#     intervals = dates.diff().dt.days.dropna()\n", "#     return intervals.mean()\n", "#\n", "# avg_intervals = df_clean.groupby('customer_id')['order_date'].apply(\n", "#     calculate_avg_order_interval\n", "# ).reset_index()\n", "# avg_intervals.columns = ['customer_id', 'avg_days_between_orders']\n", "#\n", "# print(f\"<PERSON><PERSON><PERSON> moyen entre commandes : {avg_intervals['avg_days_between_orders'].mean():.1f} jours\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Nombre de catégories achetées et diversité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul de la diversité des achats\n", "# # Si on a une colonne catégorie produit\n", "# if 'product_category' in df_clean.columns:\n", "#     category_diversity = df_clean.groupby('customer_id')['product_category'].agg({\n", "#         'unique_categories': 'nunique',\n", "#         'most_frequent_category': lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan\n", "#     })\n", "#\n", "#     print(f\"Nombre moyen de catégories par client : {category_diversity['unique_categories'].mean():.2f}\")\n", "#\n", "# # Variabilité des montants d'achat\n", "# order_variability = df_clean.groupby('customer_id')['order_value'].agg({\n", "#     'order_std': 'std',\n", "#     'order_cv': lambda x: x.std() / x.mean() if x.mean() > 0 else 0  # Coefficient de variation\n", "# })\n", "#\n", "# print(f\"Écart-type moyen des montants : {order_variability['order_std'].mean():.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 <PERSON><PERSON> moyen, écart-type et ratios"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul des métriques avancées du panier\n", "# # Fusion avec les données RFM\n", "# rfm_enriched = rfm_manual.copy()\n", "#\n", "# # Ajout de l'ancienneté\n", "# rfm_enriched = rfm_enriched.merge(customer_lifetime[['customer_lifespan_days', 'days_since_first_order']],\n", "#                                  left_on='customer_id', right_index=True, how='left')\n", "#\n", "# # Ajout des intervalles entre commandes\n", "# rfm_enriched = rfm_enriched.merge(avg_intervals, on='customer_id', how='left')\n", "#\n", "# # Ajout de la variabilité des commandes\n", "# rfm_enriched = rfm_enriched.merge(order_variability, left_on='customer_id', right_index=True, how='left')\n", "#\n", "# # Calcul de ratios utiles\n", "# rfm_enriched['monetary_per_frequency'] = rfm_enriched['monetary_total'] / rfm_enriched['frequency']\n", "# rfm_enriched['frequency_per_day'] = rfm_enriched['frequency'] / (rfm_enriched['days_since_first_order'] + 1)\n", "#\n", "# print(f\"Dataset enrichi : {rfm_enriched.shape}\")\n", "# display(rfm_enriched.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.5 Taux de retour et indicateurs avancés (si applicable)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: <PERSON><PERSON><PERSON> d'indicateurs avancés\n", "# # Si on a des données de retour/annulation\n", "# if 'order_status' in df_clean.columns:\n", "#     return_rates = df_clean.groupby('customer_id')['order_status'].apply(\n", "#         lambda x: (x == 'canceled').sum() / len(x)\n", "#     ).reset_index()\n", "#     return_rates.columns = ['customer_id', 'cancellation_rate']\n", "#\n", "#     rfm_enriched = rfm_enriched.merge(return_rates, on='customer_id', how='left')\n", "#\n", "# # Saisonnalité des achats (si on a suffisamment d'historique)\n", "# if 'order_date' in df_clean.columns:\n", "#     seasonal_patterns = df_clean.copy()\n", "#     seasonal_patterns['month'] = seasonal_patterns['order_date'].dt.month\n", "#     seasonal_patterns['quarter'] = seasonal_patterns['order_date'].dt.quarter\n", "#\n", "#     # Concentration des achats par trimestre\n", "#     quarterly_concentration = seasonal_patterns.groupby(['customer_id', 'quarter']).size().reset_index(name='orders_in_quarter')\n", "#     max_quarter_orders = quarterly_concentration.groupby('customer_id')['orders_in_quarter'].max().reset_index()\n", "#     max_quarter_orders.columns = ['customer_id', 'max_orders_in_quarter']\n", "#\n", "#     rfm_enriched = rfm_enriched.merge(max_quarter_orders, on='customer_id', how='left')\n", "#\n", "# print(f\"Features finales : {rfm_enriched.shape[1]} variables\")\n", "# print(f\"Colonnes : {list(rfm_enriched.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Normalisation des variables\n", "\n", "### 4.1 Sélection des variables pour clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Sélection des variables pour le clustering\n", "# # Exclusion de customer_id et autres identifiants\n", "# clustering_features = [\n", "#     'recency', 'frequency', 'monetary_total', 'monetary_avg',\n", "#     'customer_lifespan_days', 'days_since_first_order',\n", "#     'avg_days_between_orders', 'order_std', 'order_cv',\n", "#     'monetary_per_frequency', 'frequency_per_day'\n", "# ]\n", "#\n", "# # Filtrer les colonnes qui existent réellement\n", "# available_features = [col for col in clustering_features if col in rfm_enriched.columns]\n", "# print(f\"Variables sélectionnées pour clustering : {len(available_features)}\")\n", "# print(available_features)\n", "#\n", "# # Création du dataset pour clustering\n", "# X = rfm_enriched[available_features].copy()\n", "#\n", "# # Vérification des valeurs manquantes\n", "# print(f\"\\nValeurs manquantes par variable :\")\n", "# print(<PERSON><PERSON>isnull().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Traitement des valeurs manquantes avant normalisation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Traitement des valeurs manquantes\n", "# # Imputation des valeurs manquantes\n", "# imputer = SimpleImputer(strategy='median')\n", "# X_imputed = pd.DataFrame(\n", "#     imputer.fit_transform(X),\n", "#     columns=X.columns,\n", "#     index=X.index\n", "# )\n", "#\n", "# print(f\"Valeurs manquantes après imputation : {X_imputed.isnull().sum().sum()}\")\n", "#\n", "# # Vérification des valeurs infinies\n", "# inf_check = np.isinf(X_imputed).sum()\n", "# if inf_check.sum() > 0:\n", "#     print(f\"⚠️ Valeurs infinies détectées : {inf_check[inf_check > 0]}\")\n", "#     # Remplacer les valeurs infinies par la médiane\n", "#     X_imputed = X_imputed.replace([np.inf, -np.inf], np.nan)\n", "#     X_imputed = pd.DataFrame(\n", "#         imputer.fit_transform(X_imputed),\n", "#         columns=X_imputed.columns,\n", "#         index=X_imputed.index\n", "#     )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Standardisation via StandardScaler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Standardisation des variables\n", "# # Standardisation (moyenne=0, écart-type=1)\n", "# scaler = StandardScaler()\n", "# X_scaled = pd.DataFrame(\n", "#     scaler.fit_transform(X_imputed),\n", "#     columns=X_imputed.columns,\n", "#     index=X_imputed.index\n", "# )\n", "#\n", "# print(f\"Dataset normalisé : {X_scaled.shape}\")\n", "# print(f\"\\nMoyennes après standardisation (doivent être ~0) :\")\n", "# print(X_scaled.mean().round(6))\n", "# print(f\"\\nÉcarts-types après standardisation (doivent être ~1) :\")\n", "# print(X_scaled.std().round(6))\n", "#\n", "# # Visualisation des distributions après normalisation\n", "# fig, axes = plt.subplots(3, 4, figsize=(20, 15))\n", "# axes = axes.ravel()\n", "#\n", "# for i, col in enumerate(X_scaled.columns[:12]):\n", "#     if i < len(axes):\n", "#         axes[i].hist(X_scaled[col], bins=30, alpha=0.7)\n", "#         axes[i].set_title(f'{col} (normalisé)')\n", "#         axes[i].axvline(0, color='red', linestyle='--', alpha=0.7)\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 Export et backup des variables d'origine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Sauvegarde des versions originales et normalisées\n", "# # Sauvegarde du dataset complet avec customer_id\n", "# rfm_final = rfm_enriched.copy()\n", "# rfm_final[X_scaled.columns + '_scaled'] = X_scaled\n", "#\n", "# # Ajout des customer_id pour traçabilité\n", "# X_scaled_with_id = X_scaled.copy()\n", "# X_scaled_with_id['customer_id'] = rfm_enriched['customer_id']\n", "#\n", "# print(f\"Dataset final pour clustering : {X_scaled.shape}\")\n", "# print(f\"Dataset complet avec IDs : {X_scaled_with_id.shape}\")\n", "# print(f\"Dataset enrichi original : {rfm_final.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "### 5.1 Export du jeu final prêt à clusteriser"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Export des datasets pour le clustering\n", "# # Dataset normalisé pour clustering (sans customer_id)\n", "# X_scaled.to_csv('data/processed/features_scaled_for_clustering.csv', index=False)\n", "# X_scaled.to_pickle('data/processed/features_scaled_for_clustering.pkl')\n", "#\n", "# # Dataset avec customer_id pour traçabilité\n", "# X_scaled_with_id.to_csv('data/processed/features_scaled_with_ids.csv', index=False)\n", "# X_scaled_with_id.to_pickle('data/processed/features_scaled_with_ids.pkl')\n", "#\n", "# # Dataset complet enrichi (non normalisé)\n", "# rfm_final.to_csv('data/processed/rfm_enriched_complete.csv', index=False)\n", "# rfm_final.to_pickle('data/processed/rfm_enriched_complete.pkl')\n", "#\n", "# print(\"✅ Datasets sauvegardés :\")\n", "# print(\"- features_scaled_for_clustering.pkl : pour clustering\")\n", "# print(\"- features_scaled_with_ids.pkl : normalisé avec IDs\")\n", "# print(\"- rfm_enriched_complete.pkl : dataset complet enrichi\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Export de la liste des variables utilisées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Export de la configuration des features\n", "# import json\n", "#\n", "# feature_config = {\n", "#     'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "#     'reference_date': reference_date.strftime('%Y-%m-%d'),\n", "#     'clustering_features': list(X_scaled.columns),\n", "#     'original_shape': rfm_enriched.shape,\n", "#     'final_shape': X_scaled.shape,\n", "#     'normalization_method': 'StandardScaler',\n", "#     'imputation_strategy': 'median',\n", "#     'feature_descriptions': {\n", "#         'recency': '<PERSON><PERSON> depuis le dernier achat',\n", "#         'frequency': 'Nombre total d\\'achats',\n", "#         'monetary_total': 'Montant total des achats',\n", "#         'monetary_avg': '<PERSON><PERSON> moyen par achat',\n", "#         'customer_lifespan_days': 'Du<PERSON>e entre premier et dernier achat',\n", "#         'days_since_first_order': 'Jours depuis le premier achat',\n", "#         'avg_days_between_orders': '<PERSON><PERSON><PERSON> moyen entre commandes',\n", "#         'order_std': 'Écart-type des montants',\n", "#         'order_cv': 'Coefficient de variation des montants',\n", "#         'monetary_per_frequency': 'Montant total / fréquence',\n", "#         'frequency_per_day': 'Fréquence normalisée par jour'\n", "#     }\n", "# }\n", "#\n", "# with open('data/processed/feature_engineering_config.json', 'w') as f:\n", "#     json.dump(feature_config, f, indent=2, default=str)\n", "#\n", "# print(\"✅ Configuration des features sauvegardée\")\n", "# print(f\"Variables finales pour clustering : {len(X_scaled.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ Chargement des données nettoyées du Notebook 1\n", "- ✅ Calcul des variables RFM (<PERSON><PERSON><PERSON>, Fréquence, Montant)\n", "- ✅ Enrichissement avec variables comportementales\n", "- ✅ Traitement des valeurs manquantes et normalisation\n", "- ✅ Sauvegarde des datasets prêts pour clustering\n", "\n", "### Variables créées pour la segmentation\n", "- **Variables RFM classiques :** récence, fréquence, montant total/moyen\n", "- **Variables temporelles :** an<PERSON><PERSON><PERSON>, d<PERSON><PERSON>s entre commandes\n", "- **Variables de variabilité :** écart-type, coefficient de variation\n", "- **Variables de ratio :** montant/fréquence, fréquence/jour\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 3 :** Clustering et segmentation avec les variables préparées\n", "\n", "---\n", "\n", "**Dataset enrichi et normalisé prêt pour la segmentation !**"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}