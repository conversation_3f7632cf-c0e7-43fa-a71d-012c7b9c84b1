# Projet de Segmentation Clients Olist

## Description du Projet
Ce projet vise à développer une segmentation client pour Olist, une entreprise brésilienne de e-commerce, en utilisant des techniques d'apprentissage automatique non supervisé. L'objectif est d'identifier des groupes de clients distincts pour optimiser les stratégies marketing et améliorer l'expérience client.

## Structure du Projet

```
.
├── data/                      # Dossier contenant les données
│   ├── raw/                   # Données brutes
│   ├── clean/                 # Données nettoyées
│   └── features/              # Features engineered
├── docs/                      # Documentation du projet
├── notebooks/                 # Notebooks Jupyter
│   ├── 1_data_exploration.ipynb    # Analyse exploratoire
│   ├── 2_feature_engineering.ipynb # Ingénierie des features
│   ├── 3_clustering_segmentation.ipynb # Modélisation
│   └── 4_analysis_recommendations.ipynb # Analyse finale
├── sql/                      # Scripts SQL
│   └── queries.sql           # Requêtes pour le dashboard
├── utils/                    # Utilitaires et fonctions
└── requirements.txt          # Dépendances Python
```

## Installation et Configuration

1. Cloner le repository
2. Installer les dépendances :
```bash
pip install -r requirements.txt
```
3. Configurer l'environnement de base de données (SQLite)

## Contenu des Notebooks

1. **Analyse Exploratoire (1_data_exploration.ipynb)**
   - Exploration initiale des données
   - Analyses statistiques descriptives
   - Visualisations des distributions

2. **Feature Engineering (2_feature_engineering.ipynb)**
   - Préparation et transformation des données
   - Création de nouvelles features
   - Nettoyage des données

3. **Modélisation (3_clustering_segmentation.ipynb)**
   - Sélection et application des algorithmes de clustering
   - Optimisation des hyperparamètres
   - Évaluation des segments

4. **Analyse et Recommandations (4_analysis_recommendations.ipynb)**
   - Caractérisation des segments
   - Insights business
   - Recommandations stratégiques

## Requêtes SQL
Le fichier `sql/queries.sql` contient les requêtes demandées par l'équipe d'analyse pour le dashboard, notamment :
- Évolution des commandes mensuelles
- Distribution des délais de livraison
- Satisfaction client par catégorie
- Répartition géographique des ventes

## Utilitaires
Le dossier `utils/` contient des modules Python réutilisables pour :
- Chargement des données
- Outils d'analyse exploratoire
- Fonctions de feature engineering
- Visualisation des clusters

## Livrables
1. Script SQL pour le dashboard
2. Notebook d'analyse exploratoire
3. Notebook de modélisation
4. Notebook de simulation pour la mise à jour du modèle
5. Notebooks de recommandations et proposition de contrat de maintenance
