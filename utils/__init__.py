"""
Package utils pour la gestion des exports et des fonctions utilitaires du projet.
"""

# Imports des modules principaux pour un accès facilité
from . import (
    clustering,
    core,
    data_tools,
    feature_engineering,
    preprocessing,
    save_load,
    visualize_clusters,
    analysis_tools,
    marketing_reco,
)

# __all__ permet de définir ce qui est importé avec "from utils import *"
# Il est souvent préférable d'importer explicitement les modules ou fonctions.
__all__ = [
    "core",
    "data_tools",
    "preprocessing",
    "save_load",
    "feature_engineering",
    "clustering",
    "visualize_clusters",
    "analysis_tools",
    "marketing_reco",
]

# Note: Consolidation terminée. Tous les modules utilitaires sont maintenant disponibles.
# Les modules config.py et notebook_init.py ont été fusionnés dans core.py.
# Structure finale: 10 modules optimisés pour une utilisation efficace.
